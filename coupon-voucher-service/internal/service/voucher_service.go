package service

import (
	"context"
	"fmt"
	"math"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/repository"
	"google.golang.org/protobuf/types/known/timestamppb"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
)

type VoucherService interface {
	CreateVoucher(ctx context.Context, req *proto_voucher_v1.CreateVoucherRequest) (*proto_voucher_v1.CreateVoucherResponse, error)
	GetVoucher(ctx context.Context, req *proto_voucher_v1.GetVoucherRequest) (*proto_voucher_v1.GetVoucherResponse, error)
	GetVoucherByCode(ctx context.Context, req *proto_voucher_v1.GetVoucherByCodeRequest) (*proto_voucher_v1.GetVoucherByCodeResponse, error)
	UpdateVoucher(ctx context.Context, req *proto_voucher_v1.UpdateVoucherRequest) (*proto_voucher_v1.UpdateVoucherResponse, error)
	ListVouchers(ctx context.Context, req *proto_voucher_v1.ListVouchersRequest) (*proto_voucher_v1.ListVouchersResponse, error)

	GetDiscountTypes(ctx context.Context, req *proto_voucher_v1.GetDiscountTypesRequest) (*proto_voucher_v1.GetDiscountTypesResponse, error)

	CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error)
	ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error)
	IncrementVoucherUsage(ctx context.Context, req *proto_voucher_v1.IncrementVoucherUsageRequest) (*proto_voucher_v1.IncrementVoucherUsageResponse, error)
}

type voucherService struct {
	repo            repository.VoucherRepository
	userClient      *clients.UserClient
	orderClient     *clients.OrderClient
	eventPublisher  *VoucherEventPublisher
	logger          *logging.Logger
	businessMetrics *metrics.BusinessMetrics
}

func NewVoucherService(repo repository.VoucherRepository, userClient *clients.UserClient, orderClient *clients.OrderClient, eventPublisher *VoucherEventPublisher, logger *logging.Logger, businessMetrics *metrics.BusinessMetrics) VoucherService {
	return &voucherService{
		repo:            repo,
		userClient:      userClient,
		orderClient:     orderClient,
		eventPublisher:  eventPublisher,
		logger:          logger,
		businessMetrics: businessMetrics,
	}
}

func (s *voucherService) CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error) {
	start := time.Now()
	modelReq := &model.VoucherEligibilityRequest{
		VoucherCode:    req.VoucherCode,
		UserID:         req.UserId,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: req.OrderTimestamp.AsTime(),
		CartItems:      convertProtoCartItems(req.CartItems),
	}

	resp, err := s.repo.CheckVoucherEligibility(ctx, modelReq)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to check voucher eligibility: %v", err))
	}

	var voucherID uint64
	if resp.VoucherID != nil {
		voucherID = *resp.VoucherID
	}

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.CheckVoucherEligibilityResponse{
		Eligible:       resp.Eligible,
		Message:        resp.Message,
		VoucherId:      voucherID,
		DiscountAmount: resp.DiscountAmount,
	}, nil
}

func (s *voucherService) ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error) {
	start := time.Now()
	modelReq := &model.AutoVoucherEligibilityRequest{
		UserID:         req.UserId,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: req.OrderTimestamp.AsTime(),
		CartItems:      convertProtoCartItems(req.CartItems),
	}

	eligibleVouchers, err := s.repo.GetEligibleAutoVouchers(ctx, modelReq)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to retrieve eligible vouchers: %v", err))
	}

	var responseVouchers []*proto_voucher_v1.EligibleVoucherInfo
	for _, voucher := range eligibleVouchers {
		responseVouchers = append(responseVouchers, &proto_voucher_v1.EligibleVoucherInfo{
			Eligible:       voucher.Eligible,
			Voucher:        convertVoucherToProtoVoucher(voucher.Voucher),
			DiscountAmount: voucher.DiscountAmount,
		})
	}

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.ListAutoEligibleVouchersResponse{Vouchers: responseVouchers}, nil
}

func convertProtoCartItems(protoItems []*proto_voucher_v1.CartItem) []model.CartItem {
	var items []model.CartItem
	for _, item := range protoItems {
		modelItem := model.CartItem{
			Quantity: int(item.Quantity),
			Price:    item.Price,
		}
		if item.ProductId != nil {
			modelItem.ProductID = item.ProductId
		}
		if item.CategoryId != nil {
			modelItem.CategoryID = item.CategoryId
		}
		items = append(items, modelItem)
	}
	return items
}

func (s *voucherService) CreateVoucher(ctx context.Context, req *proto_voucher_v1.CreateVoucherRequest) (*proto_voucher_v1.CreateVoucherResponse, error) {
	start := time.Now()
	modelReq := &model.CreateVoucherRequest{
		VoucherCode:    req.VoucherCode,
		Title:          req.Title,
		Description:    req.Description,
		DiscountTypeID: req.DiscountTypeId,
		DiscountValue:  req.DiscountValue,
		UsageMethod:    convertProtoUsageMethod(req.UsageMethod),
		ValidFrom:      req.ValidFrom.AsTime(),
		ValidUntil:     req.ValidUntil.AsTime(),
		MinOrderAmount: req.MinOrderAmount,
	}

	if req.MaxUsageCount != nil {
		count := int(*req.MaxUsageCount)
		modelReq.MaxUsageCount = &count
	}

	if req.MaxUsagePerUser != nil {
		count := int(*req.MaxUsagePerUser)
		modelReq.MaxUsagePerUser = &count
	}

	if req.MaxDiscountAmount != nil {
		modelReq.MaxDiscountAmount = req.MaxDiscountAmount
	}

	var createdBy uint64
	if id, ok := auth.GetUserIDFromContext(ctx); ok {
		createdBy = id
	} else if req.Metadata != nil {
		createdBy = req.Metadata.UserId
	}

	voucher, err := s.repo.Create(ctx, modelReq, createdBy)
	if err != nil {
		s.businessMetrics.RecordVoucherCreation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to create voucher: %v", err))
	}

	if s.eventPublisher != nil {
		go func() {
			if err := s.eventPublisher.PublishVoucherCreated(context.Background(), voucher); err != nil {
				s.logger.Errorf("Failed to publish voucher created event: %v", err)
			}
		}()
	}

	s.businessMetrics.RecordVoucherCreation("success", time.Since(start))
	return &proto_voucher_v1.CreateVoucherResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) GetVoucher(ctx context.Context, req *proto_voucher_v1.GetVoucherRequest) (*proto_voucher_v1.GetVoucherResponse, error) {
	start := time.Now()
	voucher, err := s.repo.GetByID(ctx, req.VoucherId)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewNotFoundError(fmt.Sprintf("voucher with ID '%d' not found", req.VoucherId))
	}

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.GetVoucherResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) GetVoucherByCode(ctx context.Context, req *proto_voucher_v1.GetVoucherByCodeRequest) (*proto_voucher_v1.GetVoucherByCodeResponse, error) {
	start := time.Now()
	voucher, err := s.repo.GetByCode(ctx, req.VoucherCode)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewNotFoundError(fmt.Sprintf("voucher with code '%s' not found", req.VoucherCode))
	}

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.GetVoucherByCodeResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) UpdateVoucher(ctx context.Context, req *proto_voucher_v1.UpdateVoucherRequest) (*proto_voucher_v1.UpdateVoucherResponse, error) {
	start := time.Now()
	modelReq := &model.UpdateVoucherRequest{
		Title:          req.Title,
		Description:    req.Description,
		DiscountTypeID: req.DiscountTypeId,
		DiscountValue:  req.DiscountValue,
		UsageMethod:    convertProtoUsageMethod(req.UsageMethod),
		Status:         convertProtoVoucherStatus(req.Status),
		MinOrderAmount: req.MinOrderAmount,
		ValidFrom:      req.ValidFrom.AsTime(),
		ValidUntil:     req.ValidUntil.AsTime(),
	}

	if req.MaxUsageCount != nil {
		count := int(*req.MaxUsageCount)
		modelReq.MaxUsageCount = &count
	}

	if req.MaxUsagePerUser != nil {
		count := int(*req.MaxUsagePerUser)
		modelReq.MaxUsagePerUser = &count
	}

	if req.MaxDiscountAmount != nil {
		modelReq.MaxDiscountAmount = req.MaxDiscountAmount
	}

	err := s.repo.Update(ctx, req.VoucherId, modelReq)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to update voucher: %v", err))
	}

	voucher, err := s.repo.GetByID(ctx, req.VoucherId)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to retrieve updated voucher: %v", err))
	}

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.UpdateVoucherResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) ListVouchers(ctx context.Context, req *proto_voucher_v1.ListVouchersRequest) (*proto_voucher_v1.ListVouchersResponse, error) {
	start := time.Now()
	modelReq := &model.ListVouchersRequest{
		Page:      int(req.Page),
		Limit:     int(req.Limit),
		Search:    req.Search,
		Status:    req.Status,
		SortBy:    req.SortBy,
		SortOrder: req.SortOrder,
	}

	if req.DiscountTypeId != nil {
		modelReq.DiscountTypeID = req.DiscountTypeId
	}

	if req.UsageMethod != nil {
		usageMethod := convertProtoUsageMethod(*req.UsageMethod)
		modelReq.UsageMethod = &usageMethod
	}

	vouchers, total, err := s.repo.List(ctx, modelReq)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to list vouchers: %v", err))
	}

	var protoVouchers []*proto_voucher_v1.Voucher
	for _, voucher := range vouchers {
		protoVouchers = append(protoVouchers, convertVoucherToProtoVoucher(voucher))
	}

	totalPages := int(math.Ceil(float64(total) / float64(modelReq.Limit)))

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.ListVouchersResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Vouchers:   protoVouchers,
		Total:      int32(total),
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: int32(totalPages),
	}, nil
}

func (s *voucherService) GetDiscountTypes(ctx context.Context, req *proto_voucher_v1.GetDiscountTypesRequest) (*proto_voucher_v1.GetDiscountTypesResponse, error) {
	start := time.Now()
	discountTypes, err := s.repo.GetDiscountTypes(ctx)
	if err != nil {
		s.businessMetrics.RecordVoucherValidation("error", time.Since(start))
		return nil, errors.NewInternalError(fmt.Sprintf("failed to get discount types: %v", err))
	}

	var protoDiscountTypes []*proto_voucher_v1.DiscountType
	for _, dt := range discountTypes {
		protoDiscountTypes = append(protoDiscountTypes, &proto_voucher_v1.DiscountType{
			Id:          dt.ID,
			TypeCode:    dt.TypeCode,
			TypeName:    dt.TypeName,
			Description: dt.Description,
			IsActive:    dt.IsActive,
			CreatedAt:   timestamppb.New(dt.CreatedAt),
			UpdatedAt:   timestamppb.New(dt.UpdatedAt),
		})
	}

	s.businessMetrics.RecordVoucherValidation("success", time.Since(start))
	return &proto_voucher_v1.GetDiscountTypesResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		DiscountTypes: protoDiscountTypes,
	}, nil
}

func convertProtoUsageMethod(protoMethod proto_voucher_v1.UsageMethod) model.UsageMethod {
	switch protoMethod {
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL:
		return model.UsageMethodManual
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO:
		return model.UsageMethodAutomatic
	default:
		return model.UsageMethodManual
	}
}

func convertModelUsageMethod(modelMethod model.UsageMethod) proto_voucher_v1.UsageMethod {
	switch modelMethod {
	case model.UsageMethodManual:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	case model.UsageMethodAutomatic:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO
	default:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	}
}

func convertProtoVoucherStatus(protoStatus proto_voucher_v1.VoucherStatus) string {
	switch protoStatus {
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE:
		return string(model.VoucherStatusActive)
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE:
		return string(model.VoucherStatusInactive)
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED:
		return string(model.VoucherStatusExpired)
	default:
		return string(model.VoucherStatusActive)
	}
}

func convertVoucherToProtoVoucher(voucher *model.Voucher) *proto_voucher_v1.Voucher {
	if voucher == nil {
		return nil
	}

	protoVoucher := &proto_voucher_v1.Voucher{
		Id:                voucher.ID,
		VoucherCode:       voucher.VoucherCode,
		Title:             voucher.Title,
		Description:       voucher.Description,
		DiscountTypeId:    voucher.DiscountTypeID,
		DiscountValue:     voucher.DiscountValue,
		UsageMethod:       convertModelUsageMethod(voucher.UsageMethod),
		ValidFrom:         timestamppb.New(voucher.ValidFrom),
		ValidUntil:        timestamppb.New(voucher.ValidUntil),
		CurrentUsageCount: int32(voucher.CurrentUsageCount),
		MinOrderAmount:    voucher.MinOrderAmount,
		CreatedBy:         voucher.CreatedBy,
		CreatedAt:         timestamppb.New(voucher.CreatedAt),
		UpdatedAt:         timestamppb.New(voucher.UpdatedAt),
		TotalSavings:      voucher.TotalSavings,
		UniqueUsers:       int32(voucher.UniqueUsers),
	}

	if voucher.MaxUsageCount != nil {
		count := int32(*voucher.MaxUsageCount)
		protoVoucher.MaxUsageCount = &count
	}

	if voucher.MaxUsagePerUser != nil {
		count := int32(*voucher.MaxUsagePerUser)
		protoVoucher.MaxUsagePerUser = &count
	}

	if voucher.MaxDiscountAmount != nil {
		protoVoucher.MaxDiscountAmount = voucher.MaxDiscountAmount
	}

	switch voucher.Status {
	case model.VoucherStatusActive:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	case model.VoucherStatusInactive:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE
	case model.VoucherStatusExpired:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED
	default:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	}

	return protoVoucher
}

func (s *voucherService) IncrementVoucherUsage(ctx context.Context, req *proto_voucher_v1.IncrementVoucherUsageRequest) (*proto_voucher_v1.IncrementVoucherUsageResponse, error) {
	start := time.Now()
	log := s.logger.WithContext(ctx)
	log.Infof("Incrementing usage for voucher %d by user %d", req.VoucherId, req.UserId)

	voucher, err := s.repo.GetByID(ctx, req.VoucherId)
	if err != nil {
		log.Errorf("Failed to get voucher %d: %v", req.VoucherId, err)
		s.businessMetrics.RecordVoucherRedemption("error", time.Since(start))
		return &proto_voucher_v1.IncrementVoucherUsageResponse{
			Metadata: &proto_common_v1.ResponseMetadata{
				RequestId: req.Metadata.RequestId,
				Timestamp: timestamppb.Now(),
			},
			Success: false,
			Message: "Voucher not found",
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_NOT_FOUND,
				Message: "Voucher not found",
			},
		}, nil
	}

	if voucher.Status != model.VoucherStatusActive {
		log.Warnf("Attempted to increment usage for inactive voucher %d", req.VoucherId)
		s.businessMetrics.RecordVoucherRedemption("error", time.Since(start))
		return &proto_voucher_v1.IncrementVoucherUsageResponse{
			Metadata: &proto_common_v1.ResponseMetadata{
				RequestId: req.Metadata.RequestId,
				Timestamp: timestamppb.Now(),
			},
			Success: false,
			Message: "Voucher is not active",
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_FAILED_PRECONDITION,
				Message: "Voucher is not active",
			},
		}, nil
	}

	if req.OrderTimestamp.AsTime().After(voucher.ValidUntil) {
		log.Warnf("Attempted to increment usage for expired voucher %d", req.VoucherId)
		s.businessMetrics.RecordVoucherRedemption("error", time.Since(start))
		return &proto_voucher_v1.IncrementVoucherUsageResponse{
			Metadata: &proto_common_v1.ResponseMetadata{
				RequestId: req.Metadata.RequestId,
				Timestamp: timestamppb.Now(),
			},
			Success: false,
			Message: "Voucher has expired",
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_FAILED_PRECONDITION,
				Message: "Voucher has expired",
			},
		}, nil
	}

	if voucher.MaxUsageCount != nil && voucher.CurrentUsageCount >= *voucher.MaxUsageCount {
		log.Warnf("Attempted to increment usage for voucher %d that has reached max usage (%d)", req.VoucherId, *voucher.MaxUsageCount)
		s.businessMetrics.RecordVoucherRedemption("error", time.Since(start))
		return &proto_voucher_v1.IncrementVoucherUsageResponse{
			Metadata: &proto_common_v1.ResponseMetadata{
				RequestId: req.Metadata.RequestId,
				Timestamp: timestamppb.Now(),
			},
			Success: false,
			Message: "Voucher has reached maximum usage limit",
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_FAILED_PRECONDITION,
				Message: "Voucher has reached maximum usage limit",
			},
		}, nil
	}

	newUsageCount, err := s.repo.IncrementUsageCount(ctx, req.VoucherId)
	if err != nil {
		log.Errorf("Failed to increment usage count for voucher %d: %v", req.VoucherId, err)
		s.businessMetrics.RecordVoucherRedemption("error", time.Since(start))
		return &proto_voucher_v1.IncrementVoucherUsageResponse{
			Metadata: &proto_common_v1.ResponseMetadata{
				RequestId: req.Metadata.RequestId,
				Timestamp: timestamppb.Now(),
			},
			Success: false,
			Message: "Failed to increment voucher usage",
			Error: &proto_common_v1.ServiceError{
				Code:    proto_common_v1.StatusCode_STATUS_CODE_INTERNAL,
				Message: "Failed to increment voucher usage",
			},
		}, nil
	}

	log.Infof("Successfully incremented usage for voucher %d to %d", req.VoucherId, newUsageCount)

	if s.eventPublisher != nil {
		go func() {
			if err := s.eventPublisher.PublishVoucherUsed(context.Background(), req.VoucherId, voucher.VoucherCode, req.UserId, req.OrderId, req.DiscountAmount, req.OrderAmount); err != nil {
				s.logger.Errorf("Failed to publish voucher used event: %v", err)
			}
		}()
	}

	s.businessMetrics.RecordVoucherRedemption("success", time.Since(start))
	return &proto_voucher_v1.IncrementVoucherUsageResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.RequestId,
			Timestamp: timestamppb.Now(),
		},
		Success:       true,
		Message:       "Voucher usage incremented successfully",
		NewUsageCount: int32(newUsageCount),
	}, nil
}
