package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
)

type VoucherEventPublisher struct {
	eventPublisher *kafka.EventPublisher
	logger         *logging.Logger
	config         *config.Config
}

func NewVoucherEventPublisher(cfg *config.Config, logger *logging.Logger) *VoucherEventPublisher {
	eventPublisher := kafka.NewEventPublisher(&cfg.Kafka, logger, cfg.Service.Name, cfg.Service.Version)

	return &VoucherEventPublisher{
		eventPublisher: eventPublisher,
		logger:         logger,
		config:         cfg,
	}
}

type VoucherCreatedEvent struct {
	VoucherID           uint64    `json:"voucher_id"`
	VoucherCode         string    `json:"voucher_code"`
	Title               string    `json:"title"`
	CreatedBy           uint64    `json:"created_by"`
	ValidFrom           time.Time `json:"valid_from"`
	ValidUntil          time.Time `json:"valid_until"`
	DiscountValue       float64   `json:"discount_value"`
	UserEligibilityType string    `json:"user_eligibility_type"`
}

type VoucherUsedEvent struct {
	VoucherID      uint64  `json:"voucher_id"`
	VoucherCode    string  `json:"voucher_code"`
	UserID         uint64  `json:"user_id"`
	OrderID        uint64  `json:"order_id"`
	DiscountAmount float64 `json:"discount_amount"`
	OrderAmount    float64 `json:"order_amount"`
}

type VoucherStatusChangedEvent struct {
	VoucherID   uint64 `json:"voucher_id"`
	VoucherCode string `json:"voucher_code"`
	OldStatus   string `json:"old_status"`
	NewStatus   string `json:"new_status"`
	Reason      string `json:"reason"`
}

type VoucherExpiringEvent struct {
	VoucherID        uint64    `json:"voucher_id"`
	VoucherCode      string    `json:"voucher_code"`
	ExpiresAt        time.Time `json:"expires_at"`
	HoursUntilExpiry int32     `json:"hours_until_expiry"`
	EligibleUserIDs  []uint64  `json:"eligible_user_ids"`
}

func (vep *VoucherEventPublisher) PublishVoucherCreated(ctx context.Context, voucher *model.Voucher) error {
	log := vep.logger.WithContext(ctx)

	event := VoucherCreatedEvent{
		VoucherID:           voucher.ID,
		VoucherCode:         voucher.VoucherCode,
		Title:               voucher.Title,
		CreatedBy:           voucher.CreatedBy,
		ValidFrom:           voucher.ValidFrom,
		ValidUntil:          voucher.ValidUntil,
		DiscountValue:       voucher.DiscountValue,
		UserEligibilityType: "ALL", // Default value since eligibility is now handled via separate table
	}

	key := fmt.Sprintf("voucher:%d", voucher.ID)

	if err := vep.eventPublisher.PublishEvent(ctx, vep.config.Kafka.Topics.VoucherEvents, "voucher.created", event, key); err != nil {
		log.Errorf("Failed to publish voucher created event for voucher %d: %v", voucher.ID, err)
		return err
	}

	log.Infof("Published voucher created event for voucher %d", voucher.ID)
	return nil
}

func (vep *VoucherEventPublisher) PublishVoucherUsed(ctx context.Context, voucherID uint64, voucherCode string, userID, orderID uint64, discountAmount, orderAmount float64) error {
	log := vep.logger.WithContext(ctx)

	event := VoucherUsedEvent{
		VoucherID:      voucherID,
		VoucherCode:    voucherCode,
		UserID:         userID,
		OrderID:        orderID,
		DiscountAmount: discountAmount,
		OrderAmount:    orderAmount,
	}

	key := fmt.Sprintf("voucher:%d:user:%d", voucherID, userID)

	if err := vep.eventPublisher.PublishEvent(ctx, vep.config.Kafka.Topics.VoucherEvents, "voucher.used", event, key); err != nil {
		log.Errorf("Failed to publish voucher used event for voucher %d: %v", voucherID, err)
		return err
	}

	log.Infof("Published voucher used event for voucher %d by user %d", voucherID, userID)
	return nil
}

func (vep *VoucherEventPublisher) PublishVoucherStatusChanged(ctx context.Context, voucher *model.Voucher, oldStatus string, reason string) error {
	log := vep.logger.WithContext(ctx)

	event := VoucherStatusChangedEvent{
		VoucherID:   voucher.ID,
		VoucherCode: voucher.VoucherCode,
		OldStatus:   oldStatus,
		NewStatus:   string(voucher.Status),
		Reason:      reason,
	}

	key := fmt.Sprintf("voucher:%d", voucher.ID)

	if err := vep.eventPublisher.PublishEvent(ctx, vep.config.Kafka.Topics.VoucherEvents, "voucher.status_changed", event, key); err != nil {
		log.Errorf("Failed to publish voucher status changed event for voucher %d: %v", voucher.ID, err)
		return err
	}

	log.Infof("Published voucher status changed event for voucher %d: %s -> %s", voucher.ID, oldStatus, voucher.Status)
	return nil
}

func (vep *VoucherEventPublisher) PublishVoucherExpiring(ctx context.Context, voucher *model.Voucher, hoursUntilExpiry int32, eligibleUserIDs []uint64) error {
	log := vep.logger.WithContext(ctx)

	event := VoucherExpiringEvent{
		VoucherID:        voucher.ID,
		VoucherCode:      voucher.VoucherCode,
		ExpiresAt:        voucher.ValidUntil,
		HoursUntilExpiry: hoursUntilExpiry,
		EligibleUserIDs:  eligibleUserIDs,
	}

	key := fmt.Sprintf("voucher:%d", voucher.ID)

	if err := vep.eventPublisher.PublishEvent(ctx, vep.config.Kafka.Topics.VoucherEvents, "voucher.expiring", event, key); err != nil {
		log.Errorf("Failed to publish voucher expiring event for voucher %d: %v", voucher.ID, err)
		return err
	}

	log.Infof("Published voucher expiring event for voucher %d (expires in %d hours)", voucher.ID, hoursUntilExpiry)
	return nil
}

func (vep *VoucherEventPublisher) PublishVoucherExpiringBatch(ctx context.Context, vouchers []*model.Voucher, hoursUntilExpiry int32) error {
	log := vep.logger.WithContext(ctx)

	for _, voucher := range vouchers {
		if err := vep.PublishVoucherExpiring(ctx, voucher, hoursUntilExpiry, []uint64{}); err != nil {
			log.Errorf("Failed to publish expiring event for voucher %d in batch: %v", voucher.ID, err)
			continue
		}
	}

	log.Infof("Published expiring events for %d vouchers", len(vouchers))
	return nil
}

func (vep *VoucherEventPublisher) Close() error {
	return vep.eventPublisher.Close()
}
