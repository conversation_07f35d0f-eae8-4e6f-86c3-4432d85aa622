#!/bin/bash

# Script to insert all voucher data into coupon-voucher-service database
# This script will insert data from all the SQL files exactly as provided

set -e  # Exit on any error

# Function to load environment variables from .env file
load_env_file() {
    local env_files=(
        ".env"
        "coupon-voucher-service/.env"
        "../.env"
        "./.env"
    )

    for env_file in "${env_files[@]}"; do
        if [[ -f "$env_file" ]]; then
            echo "Loading environment variables from $env_file..."
            # Use a safer method to load env vars
            while IFS= read -r line; do
                # Skip comments and empty lines
                [[ $line =~ ^[[:space:]]*# ]] && continue
                [[ -z "${line// }" ]] && continue
                # Export the variable
                export "$line"
            done < "$env_file"
            return 0
        fi
    done

    echo "Warning: No .env file found in common locations. Using default values or existing environment variables."
    echo "Searched locations: ${env_files[*]}"
    return 1
}

# Load environment variables
load_env_file

# Database connection parameters
POSTGRES_CONTAINER="postgres-voucher"
DB_USER="${POSTGRES_USER:-postgres}"
DB_PASSWORD="${POSTGRES_PASSWORD:-password}"
DB_NAME="${POSTGRES_DB:-voucher_db}"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL container is accessible
check_database_connection() {
    print_status "Checking PostgreSQL container connection..."

    if ! command -v docker &> /dev/null; then
        print_error "docker command not found. Please install Docker."
        exit 1
    fi

    # Check if container is running
    if ! docker ps --format "table {{.Names}}" | grep -q "^${POSTGRES_CONTAINER}$"; then
        print_error "PostgreSQL container '${POSTGRES_CONTAINER}' is not running. Please check:"
        echo "  - Container name: $POSTGRES_CONTAINER"
        echo "  - Run 'docker ps' to see running containers"
        echo "  - Start the container if needed"
        exit 1
    fi

    # Test database connection inside container
    if ! docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to database inside container. Please check:"
        echo "  - Container: $POSTGRES_CONTAINER"
        echo "  - User: $DB_USER"
        echo "  - Database: $DB_NAME"
        echo "  - Environment variables are set correctly"
        exit 1
    fi

    print_success "PostgreSQL container connection successful"
}

# Function to execute SQL file using docker exec
execute_sql_file() {
    local file_path="$1"
    local table_name="$2"

    if [[ ! -f "$file_path" ]]; then
        print_error "SQL file not found: $file_path"
        return 1
    fi

    print_status "Inserting data into $table_name table from $file_path..."

    # Create a temporary file to capture any errors
    local temp_error_file=$(mktemp)

    # Copy SQL file to container and execute it
    if docker cp "$file_path" "$POSTGRES_CONTAINER:/tmp/$(basename "$file_path")" && \
       docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "/tmp/$(basename "$file_path")" 2>"$temp_error_file"; then
        print_success "Successfully inserted data into $table_name table"

        # Get row count
        local count=$(docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' ')
        print_status "Total rows in $table_name: $count"

        # Clean up the copied file
        docker exec "$POSTGRES_CONTAINER" rm -f "/tmp/$(basename "$file_path")"
    else
        print_error "Failed to insert data into $table_name table"
        if [[ -s "$temp_error_file" ]]; then
            print_error "Error details:"
            cat "$temp_error_file"
        fi
        rm -f "$temp_error_file"
        return 1
    fi

    rm -f "$temp_error_file"
}

# Function to backup existing data (optional)
backup_existing_data() {
    print_status "Creating backup of existing data..."

    local backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    # Backup each table using docker exec
    local tables=("discount_types" "vouchers" "voucher_product_restrictions" "voucher_time_restrictions" "voucher_user_eligibility")

    for table in "${tables[@]}"; do
        print_status "Backing up $table..."
        docker exec "$POSTGRES_CONTAINER" pg_dump -U "$DB_USER" -d "$DB_NAME" -t "$table" --data-only > "$backup_dir/${table}_backup.sql"
    done

    print_success "Backup created in directory: $backup_dir"
}

# Function to clear existing data
clear_existing_data() {
    print_warning "Clearing existing data from all tables..."

    # Clear data in reverse dependency order to avoid foreign key constraint issues
    docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" << EOF
-- Clear data in reverse dependency order
DELETE FROM voucher_user_eligibility;
DELETE FROM voucher_time_restrictions;
DELETE FROM voucher_product_restrictions;
DELETE FROM vouchers;
DELETE FROM discount_types;

-- Reset sequences to start from 1
SELECT setval(pg_get_serial_sequence('discount_types', 'id'), 1, false);
SELECT setval(pg_get_serial_sequence('vouchers', 'id'), 1, false);
SELECT setval(pg_get_serial_sequence('voucher_product_restrictions', 'id'), 1, false);
SELECT setval(pg_get_serial_sequence('voucher_time_restrictions', 'id'), 1, false);
SELECT setval(pg_get_serial_sequence('voucher_user_eligibility', 'id'), 1, false);
EOF

    print_success "Existing data cleared and sequences reset"
}

# Function to handle conflicts by using INSERT ... ON CONFLICT
execute_sql_with_conflict_handling() {
    local file_path="$1"
    local table_name="$2"

    if [[ ! -f "$file_path" ]]; then
        print_error "SQL file not found: $file_path"
        return 1
    fi

    print_status "Inserting data into $table_name table with conflict handling..."

    # Read the SQL file and modify it to handle conflicts
    local temp_sql_file=$(mktemp)

    # Convert INSERT statements to INSERT ... ON CONFLICT DO NOTHING
    sed 's/INSERT INTO/INSERT INTO/g' "$file_path" | \
    sed "s/);$/) ON CONFLICT (id) DO NOTHING;/g" > "$temp_sql_file"

    # Create a temporary file to capture any errors
    local temp_error_file=$(mktemp)

    # Copy modified SQL file to container and execute it
    if docker cp "$temp_sql_file" "$POSTGRES_CONTAINER:/tmp/temp_$(basename "$file_path")" && \
       docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -f "/tmp/temp_$(basename "$file_path")" 2>"$temp_error_file"; then
        print_success "Successfully inserted data into $table_name table (with conflict handling)"

        # Get row count
        local count=$(docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' ')
        print_status "Total rows in $table_name: $count"

        # Clean up the copied file
        docker exec "$POSTGRES_CONTAINER" rm -f "/tmp/temp_$(basename "$file_path")"
    else
        print_error "Failed to insert data into $table_name table"
        if [[ -s "$temp_error_file" ]]; then
            print_error "Error details:"
            cat "$temp_error_file"
        fi
        rm -f "$temp_sql_file" "$temp_error_file"
        return 1
    fi

    rm -f "$temp_sql_file" "$temp_error_file"
}

# Main execution
main() {
    print_status "Starting voucher data insertion script..."
    print_status "Target PostgreSQL container: $POSTGRES_CONTAINER"
    print_status "Target database: $DB_NAME"

    # Show loaded configuration for debugging
    print_status "Loaded configuration:"
    echo "  - Container: $POSTGRES_CONTAINER"
    echo "  - Database: $DB_NAME"
    echo "  - User: $DB_USER"
    echo "  - Password: ${DB_PASSWORD:0:3}***"
    echo ""

    # Check database connection
    check_database_connection
    
    # Ask user if they want to backup existing data
    read -p "Do you want to backup existing data before insertion? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        backup_existing_data
    fi
    
    # Ask user about insertion method
    echo "Choose insertion method:"
    echo "1. Clear existing data and insert fresh (recommended for clean setup)"
    echo "2. Insert with conflict handling (skip duplicates)"
    echo "3. Direct insert (may fail if data exists)"
    read -p "Enter your choice (1-3): " -n 1 -r
    echo

    case $REPLY in
        1)
            clear_existing_data
            print_status "Starting fresh data insertion..."
            # Insert data in dependency order
            execute_sql_file "discount_types_rows.sql" "discount_types"
            execute_sql_file "vouchers_rows.sql" "vouchers"
            execute_sql_file "voucher_product_restrictions_rows.sql" "voucher_product_restrictions"
            execute_sql_file "voucher_time_restrictions_rows.sql" "voucher_time_restrictions"
            execute_sql_file "voucher_user_eligibility_rows.sql" "voucher_user_eligibility"
            ;;
        2)
            print_status "Starting data insertion with conflict handling..."
            # Insert data in dependency order with conflict handling
            execute_sql_with_conflict_handling "discount_types_rows.sql" "discount_types"
            execute_sql_with_conflict_handling "vouchers_rows.sql" "vouchers"
            execute_sql_with_conflict_handling "voucher_product_restrictions_rows.sql" "voucher_product_restrictions"
            execute_sql_with_conflict_handling "voucher_time_restrictions_rows.sql" "voucher_time_restrictions"
            execute_sql_with_conflict_handling "voucher_user_eligibility_rows.sql" "voucher_user_eligibility"
            ;;
        3)
            print_status "Starting direct data insertion..."
            # Insert data in dependency order
            execute_sql_file "discount_types_rows.sql" "discount_types"
            execute_sql_file "vouchers_rows.sql" "vouchers"
            execute_sql_file "voucher_product_restrictions_rows.sql" "voucher_product_restrictions"
            execute_sql_file "voucher_time_restrictions_rows.sql" "voucher_time_restrictions"
            execute_sql_file "voucher_user_eligibility_rows.sql" "voucher_user_eligibility"
            ;;
        *)
            print_error "Invalid choice. Exiting."
            exit 1
            ;;
    esac

    print_success "All data insertion completed successfully!"

    # Show loaded configuration
    print_status "Configuration used:"
    echo "  - Container: $POSTGRES_CONTAINER"
    echo "  - Database: $DB_NAME"
    echo "  - User: $DB_USER"
    echo "  - Password: ${DB_PASSWORD:0:3}***"

    # Show final statistics
    print_status "Final table statistics:"
    docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" << EOF
SELECT
    'discount_types' as table_name,
    COUNT(*) as row_count
FROM discount_types
UNION ALL
SELECT
    'vouchers' as table_name,
    COUNT(*) as row_count
FROM vouchers
UNION ALL
SELECT
    'voucher_product_restrictions' as table_name,
    COUNT(*) as row_count
FROM voucher_product_restrictions
UNION ALL
SELECT
    'voucher_time_restrictions' as table_name,
    COUNT(*) as row_count
FROM voucher_time_restrictions
UNION ALL
SELECT
    'voucher_user_eligibility' as table_name,
    COUNT(*) as row_count
FROM voucher_user_eligibility;
EOF
}

# Run main function
main "$@"
