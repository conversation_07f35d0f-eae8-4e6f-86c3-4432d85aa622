-- Insert voucher service data in correct order
-- This script will populate the voucher service database with sample data

-- 1. Insert discount types first
INSERT INTO "public"."discount_types" ("id", "created_at", "type_code", "type_name", "description", "is_active", "updated_at") VALUES 
('1', '2025-06-25 06:59:06.800246+00', 'PERCENT', 'Percentage', 'Discount based on percentage of order amount', 'true', '2025-06-25 06:59:06.800246+00'), 
('2', '2025-06-25 06:59:06.800246+00', 'FIXED', 'Fixed Amount', 'Fixed amount discount from order total', 'true', '2025-06-25 06:59:06.800246+00'), 
('3', '2025-06-25 06:59:06.800246+00', 'FLAT', 'Flat Price', 'Set order to a specific flat price', 'true', '2025-06-25 06:59:06.800246+00');

-- 2. Insert vouchers (first 50 vouchers to stay within line limit)
INSERT INTO "public"."vouchers" ("id", "created_at", "voucher_code", "title", "description", "discount_type_id", "discount_value", "usage_method", "valid_from", "valid_until", "max_usage_count", "current_usage_count", "min_order_amount", "max_discount_amount", "created_by", "updated_at", "max_usage_per_user", "status") VALUES 
('112', '2024-01-01 00:00:00+00', 'WELCOME10', 'Welcome Discount', 'Get 10% off your first order', '1', '10', 'MANUAL', '2024-01-01 00:00:00+00', '2025-12-31 23:59:59+00', '1000', '0', '1175000', '2350000', '1', '2025-07-04 07:01:37.3601+00', '1', 'ACTIVE'), 
('113', '2024-06-01 00:00:00+00', 'SAVE15', 'Save 15% Today', '15% discount on all items', '1', '15', 'MANUAL', '2024-06-01 00:00:00+00', '2024-12-31 23:59:59+00', '500', '0', '2350000', '3525000', '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('114', '2024-06-01 00:00:00+00', 'SUMMER20', 'Summer Sale', '20% off summer collection', '1', '20', 'AUTO', '2024-06-01 00:00:00+00', '2024-08-31 23:59:59+00', '2000', '0', '1762500', '4700000', '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('115', '2024-01-01 00:00:00+00', 'ELECTRONICS25', 'Electronics Deal', '25% off electronics', '1', '25', 'MANUAL', '2024-01-01 00:00:00+00', '2025-06-30 23:59:59+00', '300', '0', '4700000', '7050000', '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('116', '2024-03-01 00:00:00+00', 'FASHION30', 'Fashion Flash Sale', '30% off all fashion items', '1', '30', 'AUTO', '2024-03-01 00:00:00+00', '2024-12-31 23:59:59+00', '1500', '0', '1880000', '5875000', '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('117', '2024-01-01 00:00:00+00', 'FIXED50', 'Flat $50 Off', 'Get $50 off orders over $200', '2', '1175000', 'MANUAL', '2024-01-01 00:00:00+00', '2025-12-31 23:59:59+00', '800', '0', '4700000', null, '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('118', '2024-01-01 00:00:00+00', 'SAVE100', 'Big Savings', '$100 off orders over $500', '2', '2585000', 'MANUAL', '2024-01-01 00:00:00+00', '2025-12-31 23:59:59+00', '400', '0', '11750000', null, '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('119', '2024-01-01 00:00:00+00', 'MEGA200', 'Mega Discount', '$200 off orders over $1000', '2', '4700000', 'MANUAL', '2024-01-01 00:00:00+00', '2025-12-31 23:59:59+00', '100', '0', '23500000', null, '1', '2025-07-04 09:26:42.064594+00', '1', 'ACTIVE'), 
('120', '2024-06-01 00:00:00+00', 'QUICK25', 'Quick Save', '$25 off any order', '2', '587500', 'AUTO', '2024-06-01 00:00:00+00', '2024-12-31 23:59:59+00', '2000', '0', '2350000', null, '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'), 
('121', '2024-01-01 00:00:00+00', 'NEWUSER75', 'New User Bonus', '$75 off for new customers', '2', '1762500', 'MANUAL', '2024-01-01 00:00:00+00', '2025-12-31 23:59:59+00', '1000', '0', '7050000', null, '1', '2025-06-28 01:26:14.212856+00', '1', 'ACTIVE'),
('133', '2024-02-01 00:00:00+00', 'SAVE20TODAY1', 'Special Offer #1', 'Limited time discount offer', '2', '470000', 'AUTO', '2024-02-01 00:00:00+00', '2025-02-28 23:59:59+00', '150', '0', '1175000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('134', '2024-03-01 00:00:00+00', 'SALE15WEEK2', 'Special Offer #2', 'Limited time discount offer', '1', '15', 'MANUAL', '2024-03-01 00:00:00+00', '2025-03-28 23:59:59+00', '200', '0', '1762500', '2350000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('135', '2024-04-01 00:00:00+00', 'OFF40MONTH3', 'Special Offer #3', 'Limited time discount offer', '2', '1034000', 'AUTO', '2024-04-01 00:00:00+00', '2025-04-28 23:59:59+00', '250', '0', '2350000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('136', '2024-05-01 00:00:00+00', 'PROMO25YEAR4', 'Special Offer #4', 'Limited time discount offer', '1', '25', 'MANUAL', '2024-05-01 00:00:00+00', '2025-05-28 23:59:59+00', '300', '0', '2937500', '3525000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('137', '2024-06-01 00:00:00+00', 'DISCOUNT60TIME5', 'Special Offer #5', 'Limited time discount offer', '2', '1410000', 'AUTO', '2024-06-01 00:00:00+00', '2025-06-28 23:59:59+00', '350', '0', '3525000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('138', '2024-07-01 00:00:00+00', 'SPECIAL5DEAL6', 'Special Offer #6', 'Limited time discount offer', '1', '5', 'MANUAL', '2024-07-01 00:00:00+00', '2025-07-28 23:59:59+00', '400', '0', '4112500', '4700000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('139', '2024-08-01 00:00:00+00', 'BONUS80SAVE7', 'Special Offer #7', 'Limited time discount offer', '2', '1974000', 'AUTO', '2024-08-01 00:00:00+00', '2025-08-28 23:59:59+00', '450', '0', '4700000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('140', '2024-09-01 00:00:00+00', 'EXTRA15MORE8', 'Special Offer #8', 'Limited time discount offer', '1', '15', 'MANUAL', '2024-09-01 00:00:00+00', '2025-09-28 23:59:59+00', '500', '0', '5287500', '5875000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('141', '2024-10-01 00:00:00+00', 'FLASH100BEST9', 'Special Offer #9', 'Limited time discount offer', '2', '3525000', 'AUTO', '2024-10-01 00:00:00+00', '2025-10-28 23:59:59+00', '550', '0', '5875000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'), 
('142', '2024-11-01 00:00:00+00', 'DEAL25NOW10', 'Special Offer #10', 'Limited time discount offer', '1', '25', 'MANUAL', '2024-11-01 00:00:00+00', '2025-11-28 23:59:59+00', '600', '0', '587500', '1175000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE');

-- Reset sequence for discount_types
SELECT setval('discount_types_id_seq', (SELECT MAX(id) FROM discount_types));

-- Continue with more vouchers (adding key vouchers referenced in user eligibility data)
INSERT INTO "public"."vouchers" ("id", "created_at", "voucher_code", "title", "description", "discount_type_id", "discount_value", "usage_method", "valid_from", "valid_until", "max_usage_count", "current_usage_count", "min_order_amount", "max_discount_amount", "created_by", "updated_at", "max_usage_per_user", "status") VALUES
('143', '2024-12-01 00:00:00+00', 'SAVE20TODAY11', 'Special Offer #11', 'Limited time discount offer', '2', '564000', 'AUTO', '2024-12-01 00:00:00+00', '2025-12-28 23:59:59+00', '650', '0', '1175000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('144', '2024-01-01 00:00:00+00', 'SALE5WEEK12', 'Special Offer #12', 'Limited time discount offer', '1', '5', 'MANUAL', '2024-01-01 00:00:00+00', '2025-01-28 23:59:59+00', '700', '0', '1762500', '2350000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('145', '2024-02-01 00:00:00+00', 'OFF40MONTH13', 'Special Offer #13', 'Limited time discount offer', '2', '940000', 'AUTO', '2024-02-01 00:00:00+00', '2025-02-28 23:59:59+00', '750', '0', '2350000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('146', '2024-03-01 00:00:00+00', 'PROMO15YEAR14', 'Special Offer #14', 'Limited time discount offer', '1', '15', 'MANUAL', '2024-03-01 00:00:00+00', '2025-03-28 23:59:59+00', '800', '0', '2937500', '3525000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('147', '2024-04-01 00:00:00+00', 'DISCOUNT60TIME15', 'Special Offer #15', 'Limited time discount offer', '2', '1551000', 'AUTO', '2024-04-01 00:00:00+00', '2025-04-28 23:59:59+00', '850', '0', '3525000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('148', '2024-05-01 00:00:00+00', 'SPECIAL25DEAL16', 'Special Offer #16', 'Limited time discount offer', '1', '25', 'MANUAL', '2024-05-01 00:00:00+00', '2025-05-28 23:59:59+00', '900', '0', '4112500', '4700000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('149', '2024-06-01 00:00:00+00', 'BONUS80SAVE17', 'Special Offer #17', 'Limited time discount offer', '2', '1927000', 'AUTO', '2024-06-01 00:00:00+00', '2025-06-28 23:59:59+00', '950', '0', '4700000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('150', '2024-07-01 00:00:00+00', 'EXTRA5MORE18', 'Special Offer #18', 'Limited time discount offer', '1', '5', 'MANUAL', '2024-07-01 00:00:00+00', '2025-07-28 23:59:59+00', '1000', '0', '5287500', '5875000', '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE'),
('151', '2024-08-01 00:00:00+00', 'FLASH100BEST19', 'Special Offer #19', 'Limited time discount offer', '2', '2350000', 'AUTO', '2024-08-01 00:00:00+00', '2025-08-28 23:59:59+00', '1050', '0', '5875000', null, '1', '2025-06-28 01:37:53.912169+00', '1', 'ACTIVE');

-- Reset sequence for vouchers
SELECT setval('vouchers_id_seq', (SELECT MAX(id) FROM vouchers));

-- 3. Insert voucher user eligibility data (sample data - only vouchers that exist)
INSERT INTO "public"."voucher_user_eligibility" ("id", "created_at", "voucher_id", "user_id", "user_type", "min_account_age_days", "max_account_age_days", "min_previous_orders", "max_previous_orders") VALUES
('11', '2025-07-02 03:39:42.802636+00', '151', '4', null, null, null, '2', null),
('12', '2025-07-02 03:39:42.802636+00', '148', '9', null, '52', '315', null, null),
('13', '2025-07-02 03:39:42.802636+00', '121', '8', null, '59', null, null, null),
('14', '2025-07-02 03:39:42.802636+00', '149', '3', null, null, null, '4', null),
('15', '2025-07-02 03:39:42.802636+00', '117', '9', null, null, '301', null, '29'),
('16', '2025-07-02 03:39:42.802636+00', '114', '7', null, null, null, '5', null),
('17', '2025-07-02 03:39:42.802636+00', '136', '10', null, null, '354', null, null),
('18', '2025-07-02 03:39:42.802636+00', '147', '9', null, null, '202', '3', null),
('19', '2025-07-02 03:39:42.802636+00', '139', '8', null, null, null, null, '13'),
('20', '2025-07-02 03:39:42.802636+00', '143', '3', null, '44', null, '7', '12'),
('21', '2025-07-02 03:39:42.802636+00', '120', '7', null, null, null, null, '11'),
('22', '2025-07-02 03:39:42.802636+00', '137', '4', null, null, '100', null, '28'),
('23', '2025-07-02 03:39:42.802636+00', '150', '9', null, '130', '176', null, '16'),
-- NEW user eligibility
('51', '2025-07-02 03:39:42.802636+00', '147', null, 'NEW', null, null, '0', null),
('52', '2025-07-02 03:39:42.802636+00', '151', null, 'NEW', null, null, '1', null),
('53', '2025-07-02 03:39:42.802636+00', '138', null, 'NEW', '2', null, '1', '7'),
('54', '2025-07-02 03:39:42.802636+00', '117', null, 'NEW', null, '62', '2', '5'),
('55', '2025-07-02 03:39:42.802636+00', '146', null, 'NEW', null, '55', '1', '5'),
-- VIP user eligibility
('81', '2025-07-02 03:39:42.802636+00', '140', null, 'VIP', '133', null, null, null),
('82', '2025-07-02 03:39:42.802636+00', '148', null, 'VIP', null, null, null, null),
('83', '2025-07-02 03:39:42.802636+00', '115', null, 'VIP', '142', null, null, '72'),
('84', '2025-07-02 03:39:42.802636+00', '139', null, 'VIP', null, null, '28', null),
('85', '2025-07-02 03:39:42.802636+00', '116', null, 'VIP', null, null, null, '52'),
('86', '2025-07-02 03:39:42.802636+00', '144', null, 'VIP', null, null, '23', '83'),
('111', '2025-07-04 07:01:37.3601+00', '112', '3', null, '85', null, '5', null),
('112', '2025-07-04 09:26:42.064594+00', '119', null, 'VIP', '89', null, '21', '55');

-- Reset sequence for voucher_user_eligibility
SELECT setval('voucher_user_eligibility_id_seq', (SELECT MAX(id) FROM voucher_user_eligibility));

-- 4. Insert voucher product restrictions (sample data for existing vouchers)
INSERT INTO "public"."voucher_product_restrictions" ("id", "created_at", "voucher_id", "product_id", "category_id", "is_included") VALUES
('11', '2025-07-02 07:07:55.208618+00', '135', '9', null, 'true'),
('12', '2025-07-02 07:07:55.208618+00', '118', '853', null, 'true'),
('13', '2025-07-02 07:07:55.208618+00', '113', null, '2', 'false'),
('14', '2025-07-02 07:07:55.208618+00', '138', '744', null, 'true'),
('15', '2025-07-02 07:07:55.208618+00', '118', null, '1', 'true'),
('16', '2025-07-02 07:07:55.208618+00', '151', '241', null, 'true'),
('17', '2025-07-02 07:07:55.208618+00', '146', '368', null, 'true'),
('18', '2025-07-02 07:07:55.208618+00', '139', '285', null, 'true'),
('19', '2025-07-02 07:07:55.208618+00', '146', null, '5', 'true'),
('20', '2025-07-02 07:07:55.208618+00', '143', '439', null, 'true'),
('21', '2025-07-02 07:07:55.208618+00', '118', '915', null, 'true'),
('22', '2025-07-02 07:07:55.208618+00', '120', '1024', null, 'true'),
('23', '2025-07-02 07:07:55.208618+00', '115', null, '10', 'true'),
('24', '2025-07-02 07:07:55.208618+00', '149', null, '4', 'true'),
('25', '2025-07-02 07:07:55.208618+00', '146', null, '10', 'true'),
('26', '2025-07-02 07:07:55.208618+00', '118', null, '9', 'true'),
('27', '2025-07-02 07:07:55.208618+00', '137', '86', null, 'true'),
('28', '2025-07-02 07:07:55.208618+00', '142', '966', null, 'true'),
('29', '2025-07-02 07:07:55.208618+00', '145', null, '9', 'true'),
('30', '2025-07-02 07:07:55.208618+00', '114', null, '10', 'true');

-- Reset sequence for voucher_product_restrictions
SELECT setval('voucher_product_restrictions_id_seq', (SELECT MAX(id) FROM voucher_product_restrictions));

-- 5. Insert voucher time restrictions (sample data for existing vouchers)
INSERT INTO "public"."voucher_time_restrictions" ("id", "created_at", "voucher_id", "restriction_type", "allowed_days_of_week", "allowed_hours_start", "allowed_hours_end", "specific_dates", "recurrence_pattern", "recurrence_day_of_month", "recurrence_month", "recurrence_day_of_week") VALUES
('313', '2025-07-02 06:20:54.24079+00', '113', 'DAYS_OF_WEEK', '"{5,6,0}"', null, null, null, null, null, null, null),
('314', '2025-07-02 06:20:54.24079+00', '138', 'DAYS_OF_WEEK', '"{2,4}"', null, null, null, null, null, null, null),
('315', '2025-07-02 06:20:54.24079+00', '115', 'DAYS_OF_WEEK', '"{0,6}"', null, null, null, null, null, null, null),
('316', '2025-07-02 06:20:54.24079+00', '144', 'DAYS_OF_WEEK', '"{1,3,5}"', null, null, null, null, null, null, null),
('317', '2025-07-02 06:20:54.24079+00', '120', 'DAYS_OF_WEEK', '"{0,6}"', null, null, null, null, null, null, null),
('318', '2025-07-02 06:20:54.24079+00', '150', 'DAYS_OF_WEEK', '"{0,1,6}"', null, null, null, null, null, null, null),
('319', '2025-07-02 06:20:54.24079+00', '116', 'HOURS_OF_DAY', null, '17', '23', null, null, null, null, null),
('320', '2025-07-02 06:20:54.24079+00', '114', 'HOURS_OF_DAY', null, '19', '23', null, null, null, null, null),
('321', '2025-07-02 06:20:54.24079+00', '121', 'HOURS_OF_DAY', null, '6', '12', null, null, null, null, null),
('322', '2025-07-02 06:20:54.24079+00', '140', 'HOURS_OF_DAY', null, '8', '16', null, null, null, null, null),
('323', '2025-07-02 06:20:54.24079+00', '136', 'RECURRING_DATES', null, null, null, null, 'MONTHLY', '15', null, null),
('324', '2025-07-02 06:20:54.24079+00', '117', 'RECURRING_DATES', null, null, null, null, 'WEEKLY', null, null, '4'),
('325', '2025-07-02 06:20:54.24079+00', '118', 'RECURRING_DATES', null, null, null, null, 'YEARLY', '27', '7', null),
('326', '2025-07-02 06:20:54.24079+00', '143', 'RECURRING_DATES', null, null, null, null, 'QUARTERLY', '3', null, null),
('327', '2025-07-02 06:20:54.24079+00', '151', 'SPECIFIC_DATES', null, null, null, '"{\"2025-11-11\",\"2025-12-12\"}"', null, null, null, null),
('328', '2025-07-02 06:20:54.24079+00', '142', 'SPECIFIC_DATES', null, null, null, '"{\"2025-12-25\",\"2025-12-26\"}"', null, null, null, null),
('329', '2025-07-02 06:20:54.24079+00', '139', 'SPECIFIC_DATES', null, null, null, '"{\"2025-08-15\",\"2025-11-28\"}"', null, null, null, null),
('330', '2025-07-02 06:20:54.24079+00', '135', 'SPECIFIC_DATES', null, null, null, '"{\"2025-05-01\",\"2025-06-15\",\"2025-09-01\"}"', null, null, null, null),
('331', '2025-07-02 06:20:54.24079+00', '147', 'RECURRING_DATES', null, null, null, null, 'DAILY', null, null, null),
('332', '2025-07-02 06:20:54.24079+00', '147', 'DAYS_OF_WEEK', '"{5,6,0}"', null, null, null, null, null, null, null),
('333', '2025-07-02 06:20:54.24079+00', '137', 'HOURS_OF_DAY', null, '17', '22', null, null, null, null, null),
('334', '2025-07-02 06:20:54.24079+00', '137', 'SPECIFIC_DATES', null, null, null, '"{\"2025-12-25\",\"2025-12-26\"}"', null, null, null, null),
('335', '2025-07-04 09:26:42.064594+00', '119', 'SPECIFIC_DATES', '"{}"', null, null, '"{\"2025-02-14\",\"2025-03-17\",\"2025-10-31\"}"', null, null, null, null),
('336', '2025-07-04 09:26:42.064594+00', '119', 'HOURS_OF_DAY', '"{}"', '19', '20', '"{}"', null, null, null, null);

-- Reset sequence for voucher_time_restrictions
SELECT setval('voucher_time_restrictions_id_seq', (SELECT MAX(id) FROM voucher_time_restrictions));
