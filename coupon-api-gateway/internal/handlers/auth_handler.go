package handlers

import (
	"net/http"
	"strconv"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type AuthHandler struct {
	userClient   *clients.UserClient
	logger       *logging.Logger
	cookieConfig *utils.CookieConfig
	jwtManager   *auth.JWTManager
}

func NewAuthHandler(u *clients.UserClient, l *logging.Logger, cookieConfig *utils.CookieConfig, jwtManager *auth.JWTManager) *AuthHandler {
	return &AuthHandler{
		userClient:   u,
		logger:       l,
		cookieConfig: cookieConfig,
		jwtManager:   jwtManager,
	}
}

func (h *AuthHandler) RegisterPublicRoutes(g *echo.Group, spec *docs.OpenApi) {
	registerRoute := g.POST("/register", h.HandleRegister)
	echoAdapter.AddRoute[dto.RegisterRequest, dto.RegisterResponse](spec, registerRoute, docs.OperationObject{Tags: []string{"Auth API"}})

	loginRoute := g.POST("/login", h.HandleLogin)
	echoAdapter.AddRoute[dto.LoginRequest, dto.LoginResponse](spec, loginRoute, docs.OperationObject{Tags: []string{"Auth API"}})

	logoutRoute := g.POST("/logout", h.HandleLogout)
	echoAdapter.AddRoute[struct{}, utils.SuccessResponse](spec, logoutRoute, docs.OperationObject{Tags: []string{"Auth API"}})
}

func (h *AuthHandler) HandleRegister(c echo.Context) error {
	var req dto.RegisterRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}
	if err := c.Validate(req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	ctx := c.Request().Context()

	createUserRes, err := h.userClient.CreateUser(ctx, req.Name, req.Email, req.Password)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	loginRes, err := h.userClient.Login(ctx, req.Email, req.Password)
	if err != nil {
		h.logger.WithContext(ctx).Errorf("Login failed immediately after registration for email %s: %v", req.Email, err)
		return utils.HandleGRPCError(c, err, h.logger)
	}

	token, err := h.jwtManager.GenerateToken(strconv.FormatUint(loginRes.User.Id, 10), loginRes.User.Email, loginRes.User.Role)
	if err != nil {
		h.logger.WithContext(ctx).Errorf("Failed to generate JWT token for user %s: %v", req.Email, err)
		return c.JSON(http.StatusInternalServerError, utils.NewErrorResponse("failed to generate authentication token"))
	}

	utils.SetAuthCookies(c, token, h.cookieConfig)
	userResponse := dto.ToUserResponse(createUserRes.User)

	response := &dto.RegisterResponse{
		ID:    userResponse.ID,
		Email: userResponse.Email,
		Name:  userResponse.Name,
		Role:  userResponse.Role,
		Type:  userResponse.Type,
	}

	return c.JSON(http.StatusCreated, response)
}

func (h *AuthHandler) HandleLogin(c echo.Context) error {
	var req dto.LoginRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}
	if err := c.Validate(req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	ctx := c.Request().Context()
	loginRes, err := h.userClient.Login(ctx, req.Email, req.Password)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	token, err := h.jwtManager.GenerateToken(strconv.FormatUint(loginRes.User.Id, 10), loginRes.User.Email, loginRes.User.Role)
	if err != nil {
		h.logger.WithContext(ctx).Errorf("Failed to generate JWT token for user %s: %v", req.Email, err)
		return c.JSON(http.StatusInternalServerError, utils.NewErrorResponse("failed to generate authentication token"))
	}

	utils.SetAuthCookies(c, token, h.cookieConfig)
	userResponse := dto.ToUserResponse(loginRes.User)

	return c.JSON(http.StatusOK, dto.LoginResponse{
		ID:    userResponse.ID,
		Email: userResponse.Email,
		Name:  userResponse.Name,
		Role:  userResponse.Role,
		Type:  userResponse.Type,
	})
}

func (h *AuthHandler) HandleLogout(c echo.Context) error {
	utils.ClearAuthCookies(c, h.cookieConfig)

	return c.JSON(http.StatusOK, utils.NewSuccessResponse("Logged out successfully"))
}
