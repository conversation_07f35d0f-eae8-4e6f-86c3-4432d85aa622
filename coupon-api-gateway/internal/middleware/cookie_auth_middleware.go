package middleware

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type CookieAuthConfig struct {
	JWTManager   *auth.JWTManager
	CookieConfig *utils.CookieConfig
	Logger       *logging.Logger
	SkipPaths    []string
}

func CookieAuth(config *CookieAuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if shouldSkipAuth(c.Request().URL.Path, config.SkipPaths) {
				return next(c)
			}

			accessToken, err := utils.GetAccessTokenFromCookie(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
			}

			claims, err := config.JWTManager.ValidateToken(accessToken)
			if err != nil {
				config.Logger.WithContext(c.Request().Context()).
					Debugf("Access token validation failed: %v", err)
				utils.ClearAuthCookies(c, config.CookieConfig)
				return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
			}

			// Convert userID from string to uint64
			userID, err := strconv.ParseUint(claims.UserID, 10, 64)
			if err != nil {
				config.Logger.WithContext(c.Request().Context()).
					Errorf("Invalid user ID in token: %v", err)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid token: malformed user ID")
			}

			// Set values in Go context using auth package functions
			ctx := c.Request().Context()
			ctx = auth.SetUserIDInContext(ctx, userID)
			ctx = auth.SetEmailInContext(ctx, claims.Email)
			ctx = auth.SetRoleInContext(ctx, claims.Role)

			// Update the request with the new context
			c.SetRequest(c.Request().WithContext(ctx))

			return next(c)
		}
	}
}

func shouldSkipAuth(path string, skipPaths []string) bool {
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

func RequireAuth(config *CookieAuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			accessToken, err := utils.GetAccessTokenFromCookie(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "Access token required")
			}

			claims, err := config.JWTManager.ValidateToken(accessToken)
			if err != nil {
				utils.ClearAuthCookies(c, config.CookieConfig)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid access token")
			}

			c.Set("userID", claims.UserID)
			c.Set("userEmail", claims.Email)
			c.Set("userRole", claims.Role)

			return next(c)
		}
	}
}

func RequireRole(role string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userRole, ok := c.Get("userRole").(string)
			if !ok {
				return echo.NewHTTPError(http.StatusForbidden, "User role not found")
			}

			if userRole == role {
				return next(c)
			}

			return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
		}
	}
}

// HTTPMetricsConfig holds configuration for HTTP metrics middleware
type HTTPMetricsConfig struct {
	Metrics     *metrics.Metrics
	ServiceName string
}

// HTTPMetrics returns a middleware that records HTTP request metrics
func HTTPMetrics(config *HTTPMetricsConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()

			// Call the next handler
			err := next(c)

			// Record metrics
			duration := time.Since(start)
			status := c.Response().Status
			method := c.Request().Method
			endpoint := c.Path()

			// If endpoint is empty, use the request URI
			if endpoint == "" {
				endpoint = c.Request().URL.Path
			}

			config.Metrics.RecordHTTPRequest(config.ServiceName, method, endpoint, status, duration)

			// Record request and response sizes
			requestSize := float64(c.Request().ContentLength)
			if requestSize > 0 {
				config.Metrics.RecordHTTPRequestSize(config.ServiceName, method, endpoint, requestSize)
			}

			responseSize := float64(c.Response().Size)
			if responseSize > 0 {
				config.Metrics.RecordHTTPResponseSize(config.ServiceName, method, endpoint, status, responseSize)
			}

			return err
		}
	}
}
